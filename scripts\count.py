#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
計算 raw_csv 內所有資料夾中不重複的 id 數量
"""

import pandas as pd
from pathlib import Path
import sys

def count_unique_ids():
    """計算所有 raw_csv 資料夾中不重複的 id 數量"""
    
    # 設定路徑
    raw_csv_dir = Path("../raw_csv")
    
    if not raw_csv_dir.exists():
        print(f"❌ 找不到目錄：{raw_csv_dir}")
        return
    
    print("🔍 掃描 raw_csv 目錄中的所有資料夾...")
    print("=" * 60)
    
    all_ids = set()  # 使用 set 來自動去重
    folder_stats = []  # 記錄每個資料夾的統計
    total_recipes = 0
    
    # 遍歷所有子資料夾
    for folder in raw_csv_dir.iterdir():
        if folder.is_dir():
            folder_name = folder.name
            csv_file = folder / f"{folder_name}_食譜.csv"
            
            if csv_file.exists():
                try:
                    # 讀取 CSV 檔案
                    df = pd.read_csv(csv_file, encoding='utf-8-sig')
                    
                    if 'id' in df.columns:
                        # 獲取該資料夾的 id 列表
                        folder_ids = set(df['id'].dropna().astype(str))
                        folder_count = len(folder_ids)
                        
                        # 加入總集合
                        all_ids.update(folder_ids)
                        total_recipes += len(df)
                        
                        # 記錄統計
                        folder_stats.append({
                            'folder': folder_name,
                            'recipes': len(df),
                            'unique_ids': folder_count
                        })
                        
                        print(f"📁 {folder_name}: {len(df)} 筆食譜, {folder_count} 個不重複 ID")
                        
                    else:
                        print(f"⚠️  {folder_name}: CSV 檔案中沒有 'id' 欄位")
                        
                except Exception as e:
                    print(f"❌ {folder_name}: 讀取失敗 - {e}")
            else:
                print(f"⚠️  {folder_name}: 找不到 {folder_name}_食譜.csv")
    
    # 總結報告
    print("\n" + "=" * 60)
    print("📊 統計總結")
    print("=" * 60)
    
    if folder_stats:
        print(f"🗂️  處理資料夾數量：{len(folder_stats)}")
        print(f"📄 總食譜數量：{total_recipes:,}")
        print(f"🆔 全域不重複 ID 數量：{len(all_ids):,}")
        
        # 計算重複率
        if total_recipes > 0:
            duplicate_rate = (total_recipes - len(all_ids)) / total_recipes * 100
            print(f"🔄 重複率：{duplicate_rate:.2f}%")
        
        # 詳細統計
        print(f"\n📋 各資料夾詳細統計：")
        print("-" * 60)
        print(f"{'資料夾名稱':<20} {'食譜數':<10} {'不重複ID':<10}")
        print("-" * 60)
        
        for stat in sorted(folder_stats, key=lambda x: x['recipes'], reverse=True):
            print(f"{stat['folder']:<20} {stat['recipes']:<10} {stat['unique_ids']:<10}")
        
        # 尋找可能的重複 ID
        print(f"\n🔍 重複 ID 分析：")
        print("-" * 60)
        
        # 建立 ID 到資料夾的映射
        id_to_folders = {}
        for folder in raw_csv_dir.iterdir():
            if folder.is_dir():
                folder_name = folder.name
                csv_file = folder / f"{folder_name}_食譜.csv"
                
                if csv_file.exists():
                    try:
                        df = pd.read_csv(csv_file, encoding='utf-8-sig')
                        if 'id' in df.columns:
                            for recipe_id in df['id'].dropna().astype(str):
                                if recipe_id not in id_to_folders:
                                    id_to_folders[recipe_id] = []
                                id_to_folders[recipe_id].append(folder_name)
                    except:
                        continue
        
        # 找出重複的 ID
        duplicate_ids = {id_val: folders for id_val, folders in id_to_folders.items() if len(folders) > 1}
        
        if duplicate_ids:
            print(f"發現 {len(duplicate_ids)} 個重複的 ID：")
            for i, (id_val, folders) in enumerate(list(duplicate_ids.items())[:10], 1):  # 只顯示前10個
                print(f"  {i}. ID {id_val}: 出現在 {', '.join(folders)}")
            
            if len(duplicate_ids) > 10:
                print(f"  ... 還有 {len(duplicate_ids) - 10} 個重複 ID")
        else:
            print("✅ 沒有發現重複的 ID")
    
    else:
        print("❌ 沒有找到任何有效的資料")
    
    return len(all_ids)

if __name__ == "__main__":
    unique_count = count_unique_ids()
    print(f"\n🎯 最終結果：共有 {unique_count:,} 個不重複的 ID")