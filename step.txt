## clean_recipe.py
解析結構化資料
→ 把「原始食材字串」用程式拆解成 name、quantity、unit 等欄位，方便機器讀取和分析。

清理不需要的符號
→ 例如去掉 *、多餘空白、把路徑反斜線改成斜線等，確保資料整潔、一致。

載入資料用程式清整
→ 從 CSV 檔讀取原始資料，用前面寫的函式逐筆處理，清洗後組成統一格式。

存檔
→ 將清理後、結構化的資料存成 CSV（方便檢視與簡單操作）和 JSON（方便後續程式讀取與向量化處理）。

------------------------------------------
### 完整流程順序參考：
原始資料收集 (raw_csv/)
⬇️

資料清洗與結構化（clean_recipe_csv.py）

處理 ingredients、steps 等格式問題

修正圖片路徑

儲存為 JSON / CSV（在 cleaned_csv/）
⬇️

人工或自動標註資料 → 建立 labels.csv ✅

可根據 id 或 name 標記主食材、類別、料理方式、飲食類型等

放在 cleaned_csv/
⬇️

向量化處理（NLP、embedding、分類模型訓練等）

用清理後的 JSON 或 CSV

載入對應的 labels.csv 作為 supervised learning 的標籤
⬇️

模型訓練 / 分群 / 預測等任務
-----------------------------------------------
aipe_project/
│
├── raw_csv/
├── cleaned_csv/
├── scripts/
├── labels/
│   └── 小白菜_labels.csv       ← ✅ 你現在可以建立
├── models/
│   └── tfidf_logistic.pkl      ← 訓練後儲存用
├── embeddings/
│   └── combined_text_tfidf.npy ← 可選（推薦任務用）

-------------------------------------------------
資料蒐集與前處理

食譜來源（如 icook.tw 等）

欄位包含：食譜名稱、食材、作法、標籤

資料庫設計與查詢

儲存結構化食譜

快速查詢完全符合或部分符合食材的食譜

向量化搜尋（語意相似）

食材向量建模（Embedding）

使用向量相似度判斷最接近的食譜或組合

食譜重組模型

根據輸入食材＋相似食譜組合重組新食譜

可先用 rule-based，再進階用 RAG / GPT fine-tune

前端顯示介面

輸入食材 → 顯示結果（可用 Flask + Bootstrap）
-----------------------------------------------

📁 資料面
爬蟲工具：requests, BeautifulSoup, Selenium

結構化資料：CSV / SQLite / MongoDB

🧠 NLP模型與處理
向量模型：

sentence-transformers（適合多語言 embedding）

可選模型：

中文：shibing624/text2vec

多語言：paraphrase-multilingual-MiniLM-L12-v2

相似度比對：

faiss（高效向量搜尋）

scikit-learn cosine_similarity

重組食譜（可分階段）：

v1：規則＋替換

v2：RAG（Retrieval Augmented Generation）

v3：微調 GPT（如 GPT-2 / GPT-3 fine-tune）

🧪 開發與測試工具
Flask + Jinja2（後端與模板渲染）

Docker（部署環境）

pytest（測試邏輯）

Postman / cURL（API 測試）
在建立一個delete_list.txt
並建立一個delelt_recipe.py，根據delete_list.txt內的id將cleaned_csv內的csv內該id的食譜及image內的